import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import ftplib
import os
from datetime import datetime
import threading
import queue
import time
import zipfile
import tempfile
import io
import urllib.request
import webbrowser
from PIL import Image, ImageTk

class ThemeManager:
    def __init__(self):
        self.is_dark = False
        self.colors = {
            'light': {
                'bg': '#f4f7fa',
                'fg': '#1e1e2f',
                'primary': '#007bff',
                'secondary': '#6c757d',
                'danger': '#dc3545',
                'success': '#28a745',
                'input_bg': '#ffffff',
                'input_border': '#ced4da',
                'panel_bg': '#ffffff',
                'hover_lighten': 0.2
            },
            'dark': {
                'bg': '#1e1e2f',
                'fg': '#f4f7fa',
                'primary': '#007bff',
                'secondary': '#6c757d',
                'danger': '#dc3545',
                'success': '#28a745',
                'input_bg': '#2d2d42',
                'input_border': '#3d3d5c',
                'panel_bg': '#2d2d42',
                'hover_lighten': 0.1
            }
        }
        
    def get_color(self, name):
        theme = 'dark' if self.is_dark else 'light'
        return self.colors[theme][name]
        
    def toggle_theme(self):
        self.is_dark = not self.is_dark
        return self.is_dark

class ModernButton(tk.Canvas):
    def __init__(self, parent, text, command, theme_manager, bg="#007bff", fg="white", width=100, height=40, radius=20):
        super().__init__(parent, width=width, height=height, bg=parent["bg"], highlightthickness=0)
        self.command = command
        self.bg = bg
        self.fg = fg
        self.radius = radius
        self.theme_manager = theme_manager
        
        # Düymə arxası
        self.create_round_rect(0, 0, width, height, radius, fill=bg)
        
        # Mətn
        self.create_text(width/2, height/2, text=text, fill=fg, font=("Segoe UI", 10, "bold"))
        
        # Event-lər
        self.bind("<Button-1>", self.on_click)
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)
        
    def create_round_rect(self, x1, y1, x2, y2, radius, **kwargs):
        points = [
            x1+radius, y1,
            x2-radius, y1,
            x2, y1,
            x2, y1+radius,
            x2, y2-radius,
            x2, y2,
            x2-radius, y2,
            x1+radius, y2,
            x1, y2,
            x1, y2-radius,
            x1, y1+radius,
            x1, y1
        ]
        return self.create_polygon(points, smooth=True, **kwargs)
        
    def on_click(self, event):
        self.command()
        
    def on_enter(self, event):
        self.itemconfig(1, fill=self._lighten_color(self.bg))
        
    def on_leave(self, event):
        self.itemconfig(1, fill=self.bg)
        
    def _lighten_color(self, color, factor=None):
        if factor is None:
            factor = self.theme_manager.get_color('hover_lighten')
        # Rəngi açıq edir
        r = int(color[1:3], 16)
        g = int(color[3:5], 16)
        b = int(color[5:7], 16)
        r = min(255, int(r * (1 + factor)))
        g = min(255, int(g * (1 + factor)))
        b = min(255, int(b * (1 + factor)))
        return f"#{r:02x}{g:02x}{b:02x}"

class ModernEntry(tk.Entry):
    def __init__(self, parent, theme_manager, *args, **kwargs):
        super().__init__(parent, *args, **kwargs)
        self.theme_manager = theme_manager
        self.default_bg = theme_manager.get_color('input_bg')
        self.focus_bg = theme_manager.get_color('input_bg')
        self.default_border = theme_manager.get_color('input_border')
        self.focus_border = theme_manager.get_color('primary')
        
        self.configure(
            bg=self.default_bg,
            relief="flat",
            highlightthickness=1,
            highlightbackground=self.default_border,
            highlightcolor=self.focus_border,
            font=("Segoe UI", 10),
            fg=theme_manager.get_color('fg')
        )
        
        self.bind("<FocusIn>", self.on_focus_in)
        self.bind("<FocusOut>", self.on_focus_out)
        
    def on_focus_in(self, event):
        self.configure(bg=self.focus_bg)
        
    def on_focus_out(self, event):
        self.configure(bg=self.default_bg)

class ModernListbox(tk.Listbox):
    def __init__(self, parent, theme_manager, *args, **kwargs):
        super().__init__(parent, *args, **kwargs)
        self.theme_manager = theme_manager
        self.configure(
            bg=theme_manager.get_color('input_bg'),
            relief="flat",
            borderwidth=0,
            highlightthickness=1,
            highlightbackground=theme_manager.get_color('input_border'),
            highlightcolor=theme_manager.get_color('primary'),
            font=("Segoe UI", 10),
            selectbackground=theme_manager.get_color('primary'),
            selectforeground="white",
            fg=theme_manager.get_color('fg')
        )

class FTPUploader:
    def __init__(self, root):
        self.root = root
        self.root.title("Professional FTP Yükləyici")
        self.root.geometry("1200x800")
        
        # Tema meneceri
        self.theme_manager = ThemeManager()
        self.root.configure(bg=self.theme_manager.get_color('bg'))
        
        # Stil təyinləri
        self.style = ttk.Style()
        self.style.configure("TButton", padding=6, relief="flat", background=self.theme_manager.get_color('primary'))
        self.style.configure("TLabel", background=self.theme_manager.get_color('bg'), font=("Segoe UI", 10))
        self.style.configure("TFrame", background=self.theme_manager.get_color('bg'))
        self.style.configure("TEntry", padding=5)
        self.style.configure("TProgressbar", background=self.theme_manager.get_color('primary'), troughcolor=self.theme_manager.get_color('input_border'))
        
        # FTP məlumatları
        self.ftp_host = tk.StringVar(value="ftp.farid97.tr")
        self.ftp_user = tk.StringVar(value="s965345")
        self.ftp_pass = tk.StringVar(value="cTnIZDqg")
        self.ftp_port = tk.StringVar(value="21")
        
        # Dəyişənlər
        self.selected_files = []
        self.current_path = "/"
        self.ftp = None
        self.upload_queue = queue.Queue()
        self.is_uploading = False
        
        self.create_widgets()
        
    def create_widgets(self):
        # Ana konteyner
        self.main_container = tk.Frame(self.root, bg=self.theme_manager.get_color('bg'))
        self.main_container.pack(fill="both", expand=True, padx=30, pady=30)
        
        # Başlıq
        self.header_frame = tk.Frame(self.main_container, bg=self.theme_manager.get_color('bg'))
        self.header_frame.pack(fill="x", pady=(0, 30))
        
        self.header = tk.Label(
            self.header_frame,
            text="Professional FTP Yükləyici",
            font=("Segoe UI", 28, "bold"),
            bg=self.theme_manager.get_color('bg'),
            fg=self.theme_manager.get_color('primary')
        )
        self.header.pack()
        
        # Tema dəyişdirici düymə
        self.theme_btn = ModernButton(
            self.header_frame,
            text="🌓",
            command=self.toggle_theme,
            theme_manager=self.theme_manager,
            bg=self.theme_manager.get_color('secondary'),
            width=40,
            height=40,
            radius=20
        )
        self.theme_btn.pack(side="right", padx=10)
        
        # FTP Bağlantı bölməsi
        self.connection_frame = tk.LabelFrame(
            self.main_container,
            text="FTP Bağlantı Məlumatları",
            font=("Segoe UI", 14, "bold"),
            bg=self.theme_manager.get_color('panel_bg'),
            fg=self.theme_manager.get_color('primary'),
            padx=30,
            pady=20,
            relief="flat",
            borderwidth=0
        )
        self.connection_frame.pack(fill="x", pady=(0, 30))
        
        # Host və Port
        host_frame = tk.Frame(self.connection_frame, bg=self.theme_manager.get_color('panel_bg'))
        host_frame.pack(fill="x", pady=10)
        
        self.host_label = tk.Label(
            host_frame,
            text="Host:",
            font=("Segoe UI", 12),
            bg=self.theme_manager.get_color('panel_bg'),
            fg=self.theme_manager.get_color('fg')
        )
        self.host_label.pack(side="left", padx=(0, 15))
        
        self.host_entry = ModernEntry(
            host_frame,
            self.theme_manager,
            textvariable=self.ftp_host,
            width=40
        )
        self.host_entry.pack(side="left", padx=(0, 30))
        
        self.port_label = tk.Label(
            host_frame,
            text="Port:",
            font=("Segoe UI", 12),
            bg=self.theme_manager.get_color('panel_bg'),
            fg=self.theme_manager.get_color('fg')
        )
        self.port_label.pack(side="left", padx=(0, 15))
        
        self.port_entry = ModernEntry(
            host_frame,
            self.theme_manager,
            textvariable=self.ftp_port,
            width=10
        )
        self.port_entry.pack(side="left")
        
        # İstifadəçi və Şifrə
        credentials_frame = tk.Frame(self.connection_frame, bg=self.theme_manager.get_color('panel_bg'))
        credentials_frame.pack(fill="x", pady=10)
        
        self.user_label = tk.Label(
            credentials_frame,
            text="İstifadəçi:",
            font=("Segoe UI", 12),
            bg=self.theme_manager.get_color('panel_bg'),
            fg=self.theme_manager.get_color('fg')
        )
        self.user_label.pack(side="left", padx=(0, 15))
        
        self.user_entry = ModernEntry(
            credentials_frame,
            self.theme_manager,
            textvariable=self.ftp_user,
            width=20
        )
        self.user_entry.pack(side="left", padx=(0, 30))
        
        self.pass_label = tk.Label(
            credentials_frame,
            text="Şifrə:",
            font=("Segoe UI", 12),
            bg=self.theme_manager.get_color('panel_bg'),
            fg=self.theme_manager.get_color('fg')
        )
        self.pass_label.pack(side="left", padx=(0, 15))
        
        self.pass_entry = ModernEntry(
            credentials_frame,
            self.theme_manager,
            textvariable=self.ftp_pass,
            width=20,
            show="•"
        )
        self.pass_entry.pack(side="left")
        
        # Bağlan düyməsi
        self.connect_btn = ModernButton(
            credentials_frame,
            text="Bağlan",
            command=self.connect_ftp,
            theme_manager=self.theme_manager,
            bg=self.theme_manager.get_color('primary'),
            width=120,
            height=40
        )
        self.connect_btn.pack(side="right", padx=10)
        
        # Server və Lokal panel
        self.panels_frame = tk.Frame(self.main_container, bg=self.theme_manager.get_color('bg'))
        self.panels_frame.pack(fill="both", expand=True, pady=20)
        
        # Server panel
        self.server_frame = tk.LabelFrame(
            self.panels_frame,
            text="Server Qovluqları",
            font=("Segoe UI", 14, "bold"),
            bg=self.theme_manager.get_color('panel_bg'),
            fg=self.theme_manager.get_color('primary'),
            padx=30,
            pady=20,
            relief="flat",
            borderwidth=0
        )
        self.server_frame.pack(side="left", fill="both", expand=True, padx=(0, 15))
        
        # Server toolbar
        server_toolbar = tk.Frame(self.server_frame, bg=self.theme_manager.get_color('panel_bg'))
        server_toolbar.pack(fill="x", pady=(0, 15))
        
        self.refresh_btn = ModernButton(
            server_toolbar,
            text="Yenilə",
            command=self.refresh_server,
            theme_manager=self.theme_manager,
            bg=self.theme_manager.get_color('secondary'),
            width=100,
            height=35
        )
        self.refresh_btn.pack(side="left", padx=5)
        
        # Server qovluq siyahısı
        self.folder_listbox = ModernListbox(
            self.server_frame,
            self.theme_manager,
            selectmode="extended"
        )
        self.folder_listbox.pack(side="left", fill="both", expand=True)
        self.folder_listbox.bind("<Double-Button-1>", self.change_directory)
        
        # Kontekst menyusu
        self.context_menu = tk.Menu(self.root, tearoff=0, font=("Segoe UI", 10))
        self.context_menu.add_command(label="Yeni Qovluq", command=self.create_new_folder)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Sil", command=self.delete_selected)
        self.context_menu.add_command(label="Adını Dəyiş", command=self.rename_selected)
        
        # Boş yerə sağ klik
        self.folder_listbox.bind("<Button-3>", self.show_context_menu)
        
        server_scrollbar = ttk.Scrollbar(self.server_frame)
        server_scrollbar.pack(side="right", fill="y")
        
        self.folder_listbox.config(yscrollcommand=server_scrollbar.set)
        server_scrollbar.config(command=self.folder_listbox.yview)
        
        # Lokal panel
        self.local_frame = tk.LabelFrame(
            self.panels_frame,
            text="Seçilmiş Fayllar",
            font=("Segoe UI", 14, "bold"),
            bg=self.theme_manager.get_color('panel_bg'),
            fg=self.theme_manager.get_color('primary'),
            padx=30,
            pady=20,
            relief="flat",
            borderwidth=0
        )
        self.local_frame.pack(side="right", fill="both", expand=True)
        
        # Lokal toolbar
        local_toolbar = tk.Frame(self.local_frame, bg=self.theme_manager.get_color('panel_bg'))
        local_toolbar.pack(fill="x", pady=(0, 15))
        
        self.select_file_btn = ModernButton(
            local_toolbar,
            text="Fayl Seç",
            command=self.select_files,
            theme_manager=self.theme_manager,
            bg=self.theme_manager.get_color('primary'),
            width=100,
            height=35
        )
        self.select_file_btn.pack(side="left", padx=5)
        
        self.select_folder_btn = ModernButton(
            local_toolbar,
            text="Qovluq Seç",
            command=self.select_folder,
            theme_manager=self.theme_manager,
            bg=self.theme_manager.get_color('primary'),
            width=100,
            height=35
        )
        self.select_folder_btn.pack(side="left", padx=5)
        
        self.clear_btn = ModernButton(
            local_toolbar,
            text="Təmizlə",
            command=self.clear_selection,
            theme_manager=self.theme_manager,
            bg=self.theme_manager.get_color('danger'),
            width=100,
            height=35
        )
        self.clear_btn.pack(side="left", padx=5)
        
        # Seçilmiş fayllar siyahısı
        self.file_listbox = ModernListbox(
            self.local_frame,
            self.theme_manager,
            selectmode="extended"
        )
        self.file_listbox.pack(side="left", fill="both", expand=True)
        
        # Checkbox-lar üçün dəyişənlər
        self.file_checkboxes = {}  # Fayl yolu -> Checkbox dəyişəni
        
        # Checkbox klikləməsi üçün event əlavə et
        self.file_listbox.bind("<Button-1>", self.toggle_checkbox)
        
        local_scrollbar = ttk.Scrollbar(self.local_frame)
        local_scrollbar.pack(side="right", fill="y")
        
        self.file_listbox.config(yscrollcommand=local_scrollbar.set)
        local_scrollbar.config(command=self.file_listbox.yview)
        
        # Progress bar və status
        self.status_frame = tk.Frame(self.main_container, bg=self.theme_manager.get_color('bg'))
        self.status_frame.pack(fill="x", pady=(0, 20))
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.status_frame,
            variable=self.progress_var,
            maximum=100,
            style="TProgressbar"
        )
        self.progress_bar.pack(fill="x", pady=(0, 10))
        
        self.status_var = tk.StringVar(value="Hazır")
        self.status_label = tk.Label(
            self.status_frame,
            textvariable=self.status_var,
            font=("Consolas", 12),
            bg=self.theme_manager.get_color('bg'),
            fg=self.theme_manager.get_color('fg')
        )
        self.status_label.pack(fill="x")
        
        # Yüklə düyməsi
        self.upload_btn = ModernButton(
            self.main_container,
            text="Yüklə",
            command=self.start_upload,
            theme_manager=self.theme_manager,
            bg=self.theme_manager.get_color('success'),
            width=200,
            height=50
        )
        self.upload_btn.pack(pady=20)
        
        # Klaviatura qısa yolları
        self.root.bind("<Control-o>", lambda e: self.select_files())
        self.root.bind("<Control-u>", lambda e: self.start_upload())
        
    def toggle_theme(self):
        is_dark = self.theme_manager.toggle_theme()
        
        # Ana pəncərə və konteyner
        self.root.configure(bg=self.theme_manager.get_color('bg'))
        self.main_container.configure(bg=self.theme_manager.get_color('bg'))
        
        # Başlıq
        self.header_frame.configure(bg=self.theme_manager.get_color('bg'))
        self.header.configure(
            bg=self.theme_manager.get_color('bg'),
            fg=self.theme_manager.get_color('primary')
        )
        
        # Bağlantı paneli
        self.connection_frame.configure(
            bg=self.theme_manager.get_color('panel_bg'),
            fg=self.theme_manager.get_color('primary')
        )
        
        # Input sahələri
        for entry in [self.host_entry, self.port_entry, self.user_entry, self.pass_entry]:
            entry.configure(
                bg=self.theme_manager.get_color('input_bg'),
                fg=self.theme_manager.get_color('fg'),
                highlightbackground=self.theme_manager.get_color('input_border'),
                highlightcolor=self.theme_manager.get_color('primary')
            )
        
        # Panellər
        self.panels_frame.configure(bg=self.theme_manager.get_color('bg'))
        self.server_frame.configure(
            bg=self.theme_manager.get_color('panel_bg'),
            fg=self.theme_manager.get_color('primary')
        )
        self.local_frame.configure(
            bg=self.theme_manager.get_color('panel_bg'),
            fg=self.theme_manager.get_color('primary')
        )
        
        # Siyahılar
        self.folder_listbox.configure(
            bg=self.theme_manager.get_color('input_bg'),
            fg=self.theme_manager.get_color('fg'),
            selectbackground=self.theme_manager.get_color('primary'),
            highlightbackground=self.theme_manager.get_color('input_border'),
            highlightcolor=self.theme_manager.get_color('primary')
        )
        
        self.file_listbox.configure(
            bg=self.theme_manager.get_color('input_bg'),
            fg=self.theme_manager.get_color('fg'),
            selectbackground=self.theme_manager.get_color('primary'),
            highlightbackground=self.theme_manager.get_color('input_border'),
            highlightcolor=self.theme_manager.get_color('primary')
        )
        
        # Status sahəsi
        self.status_frame.configure(bg=self.theme_manager.get_color('bg'))
        self.status_label.configure(
            bg=self.theme_manager.get_color('bg'),
            fg=self.theme_manager.get_color('fg')
        )
        
        # Progress bar
        self.style.configure(
            "TProgressbar",
            background=self.theme_manager.get_color('primary'),
            troughcolor=self.theme_manager.get_color('input_border')
        )
        
        # Düymələr
        for btn in [self.connect_btn, self.refresh_btn, self.select_file_btn, 
                   self.select_folder_btn, self.clear_btn, self.upload_btn]:
            btn.configure(bg=self.theme_manager.get_color('primary'))
        
        # Xüsusi düymələr
        self.clear_btn.configure(bg=self.theme_manager.get_color('danger'))
        self.upload_btn.configure(bg=self.theme_manager.get_color('success'))
        self.refresh_btn.configure(bg=self.theme_manager.get_color('secondary'))
        
        # Etiketlər
        for label in [self.host_label, self.port_label, self.user_label, self.pass_label]:
            label.configure(
                bg=self.theme_manager.get_color('panel_bg'),
                fg=self.theme_manager.get_color('fg')
            )

    def connect_ftp(self):
        try:
            if self.ftp:
                self.ftp.quit()
                
            self.ftp = ftplib.FTP()
            self.ftp.connect(self.ftp_host.get(), int(self.ftp_port.get()))
            self.ftp.login(self.ftp_user.get(), self.ftp_pass.get())
            self.ftp.encoding = "utf-8"
            
            # Əsas qovluğa keç
            try:
                self.ftp.cwd("/")  # Əvvəlcə root qovluğa keç
                
                # Serverdəki bütün qovluqları al
                directories = []
                self.ftp.dir(directories.append)
                
                # domains qovluğunu tap və keç
                for item in directories:
                    if "domains" in item.lower():
                        self.ftp.cwd("/domains")
                        break
                
                # Domen adını FTP hostdan al
                domain = self.ftp_host.get()
                if domain.startswith('ftp.'):
                    domain = domain[4:]  # ftp. prefiksini sil
                
                # Domen qovluğuna keç
                try:
                    self.ftp.cwd(domain)
                except:
                    # Əgər tam domen adı ilə alınmasa, ilk hissəsi ilə cəhd et
                    domain_parts = domain.split('.')
                    if len(domain_parts) > 0:
                        try:
                            self.ftp.cwd(domain_parts[0])
                        except:
                            pass
                
                # public_html qovluğuna keç
                try:
                    self.ftp.cwd("public_html")
                except:
                    # Əgər public_html yoxdursa, digər variantları yoxla
                    for folder in ["httpdocs", "www", "web"]:
                        try:
                            self.ftp.cwd(folder)
                            break
                        except:
                            continue
                
                self.current_path = self.ftp.pwd()
                self.refresh_server()
                self.status_var.set(f"Bağlantı uğurlu - Cari qovluq: {self.current_path}")
                
            except Exception as e:
                self.status_var.set(f"Qovluğa keçid xətası: {str(e)}")
                messagebox.showwarning("Xəbərdarlıq", 
                    "Avtomatik qovluğa keçid alınmadı.\n" +
                    "Zəhmət olmasa, server qovluqlarından manual olaraq keçid edin.")
                self.refresh_server()
                
        except Exception as e:
            messagebox.showerror("Xəta", f"Bağlantı xətası: {str(e)}")
            self.status_var.set("Bağlantı xətası")
            
    def refresh_server(self):
        if not self.ftp:
            return
            
        self.folder_listbox.delete(0, tk.END)
        try:
            self.folder_listbox.insert(tk.END, "../")
            items = []
            self.ftp.dir(items.append)
            
            for item in items:
                # FTP LIST formatını parse et
                parts = item.split()
                name = " ".join(parts[8:])
                if name not in [".", ".."]:
                    prefix = "📁 " if item.startswith("d") else "📄 "
                    self.folder_listbox.insert(tk.END, prefix + name)
                    
        except Exception as e:
            messagebox.showerror("Xəta", f"Qovluq siyahısı alına bilmədi: {str(e)}")
            
    def change_directory(self, event):
        if not self.ftp:
            return
            
        selection = self.folder_listbox.curselection()
        if not selection:
            return
            
        item = self.folder_listbox.get(selection[0])
        
        if item == "../":
            try:
                self.ftp.cwd("..")
                self.current_path = self.ftp.pwd()
                self.refresh_server()
                self.status_var.set(f"Cari qovluq: {self.current_path}")
            except Exception as e:
                if "550" in str(e):
                    messagebox.showwarning("Xəbərdarlıq", "Bu qovluğa keçid icazəniz yoxdur!")
                else:
                    messagebox.showerror("Xəta", f"Qovluğa keçid xətası: {str(e)}")
            return
            
        # Prefixi sil
        name = item[2:] if item.startswith(("📁 ", "📄 ")) else item
        
        try:
            # Əvvəlcə yoxla
            self.ftp.size(name)
            messagebox.showinfo("Məlumat", "Bu bir fayl, qovluq deyil")
            return
        except:
            try:
                self.ftp.cwd(name)
                self.current_path = self.ftp.pwd()
                self.refresh_server()
                self.status_var.set(f"Cari qovluq: {self.current_path}")
            except Exception as e:
                if "550" in str(e):
                    messagebox.showwarning("Xəbərdarlıq", "Bu qovluğa keçid icazəniz yoxdur!")
                else:
                    messagebox.showerror("Xəta", f"Qovluğa keçid xətası: {str(e)}")
                
    def create_new_folder(self):
        if not self.ftp:
            messagebox.showwarning("Xəbərdarlıq", "Əvvəlcə FTP serverə bağlanın!")
            return
            
        folder_name = simpledialog.askstring("Yeni Qovluq", "Qovluq adını daxil edin:")
        if folder_name:
            try:
                self.ftp.mkd(folder_name)
                self.refresh_server()
                self.status_var.set(f"Yeni qovluq yaradıldı: {folder_name}")
            except Exception as e:
                messagebox.showerror("Xəta", f"Qovluq yaradıla bilmədi: {str(e)}")
                
    def select_files(self):
        files = filedialog.askopenfilenames(
            title="Faylları seçin",
            filetypes=(
                ("Bütün fayllar", "*.*"),
                ("HTML faylları", "*.html;*.htm"),
                ("PHP faylları", "*.php"),
                ("CSS faylları", "*.css"),
                ("JavaScript faylları", "*.js"),
                ("Şəkil faylları", "*.jpg;*.jpeg;*.png;*.gif"),
                ("SQL faylları", "*.sql")
            )
        )
        
        self._add_files_to_list(files)
        
    def select_folder(self):
        folder = filedialog.askdirectory(title="Qovluğu seçin")
        if not folder:
            return
            
        files = []
        for root, dirs, filenames in os.walk(folder):
            # Gizli qovluqları keç
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for filename in filenames:
                # Gizli faylları keç
                if filename.startswith('.'):
                    continue
                files.append(os.path.join(root, filename))
                
        self._add_files_to_list(files)
        
    def _add_files_to_list(self, files):
        base_dir = os.path.commonpath(files) if files else ""
        
        for file_path in files:
            if file_path not in self.selected_files:
                self.selected_files.append(file_path)
                display_path = os.path.relpath(file_path, base_dir)
                
                # Checkbox yarat
                var = tk.BooleanVar(value=True)
                self.file_checkboxes[file_path] = var
                
                # Checkbox və fayl adını birlikdə əlavə et
                self.file_listbox.insert(tk.END, f"☑ {display_path}" if var.get() else f"☐ {display_path}")
                
        self.status_var.set(f"{len(self.selected_files)} fayl seçildi")
        
    def clear_selection(self):
        self.selected_files.clear()
        self.file_checkboxes.clear()
        self.file_listbox.delete(0, tk.END)
        self.status_var.set("Seçim təmizləndi")
        
    def start_upload(self):
        if not self.ftp:
            messagebox.showwarning("Xəbərdarlıq", "Əvvəlcə FTP serverə bağlanın!")
            return
            
        # Yalnız seçilmiş faylları al
        files_to_upload = [f for f in self.selected_files if self.file_checkboxes.get(f, tk.BooleanVar(value=True)).get()]
        
        if not files_to_upload:
            messagebox.showwarning("Xəbərdarlıq", "Yükləmək üçün fayl seçilməyib!")
            return
            
        if self.is_uploading:
            return
            
        self.is_uploading = True
        threading.Thread(target=self._upload_files, args=(files_to_upload,), daemon=True).start()
        
    def _create_remote_directory(self, remote_path):
        """Rekursiv olaraq qovluq strukturu yaradır və ya mövcud qovluğa keçir"""
        if remote_path.startswith('/'):
            remote_path = remote_path[1:]
        
        parts = remote_path.split('/')
        current = ''
        
        # Əvvəlcə cari mövqeyi yadda saxla
        original_dir = self.ftp.pwd()
        
        try:
            for part in parts:
                if not part:
                    continue
                    
                if current:
                    current = f"{current}/{part}"
                else:
                    current = part
                    
                try:
                    # Əvvəlcə qovluğa keçməyə çalış
                    self.ftp.cwd(current)
                except:
                    try:
                        # Qovluq yoxdursa, yarat
                        self.ftp.mkd(current)
                        self.ftp.cwd(current)
                    except Exception as e:
                        if "550" in str(e) and "File exists" in str(e):
                            # Qovluq artıq mövcuddur, keç
                            self.ftp.cwd(current)
                        else:
                            raise e
        except Exception as e:
            # Xəta baş verərsə əvvəlki mövqeyə qayıt
            self.ftp.cwd(original_dir)
            raise e
            
        # Əvvəlki mövqeyə qayıt
        self.ftp.cwd(original_dir)
        
    def _create_zip_file(self, files):
        """Seçilmiş faylları ZIP faylına çevirir"""
        # Müvəqqəti ZIP faylı yarat
        temp_zip = tempfile.NamedTemporaryFile(delete=False, suffix='.zip')
        temp_zip.close()
        
        try:
            with zipfile.ZipFile(temp_zip.name, 'w', zipfile.ZIP_DEFLATED) as zipf:
                base_dir = os.path.commonpath(files) if len(files) > 1 else os.path.dirname(files[0])
                for file in files:
                    # ZIP-də saxlanılacaq yol
                    arcname = os.path.relpath(file, base_dir)
                    zipf.write(file, arcname)
                    
            return temp_zip.name
        except Exception as e:
            if os.path.exists(temp_zip.name):
                os.unlink(temp_zip.name)
            raise e
            
    def _extract_zip_on_server(self, zip_path, extract_path):
        """Serverdə ZIP faylını aç"""
        try:
            # Cari qovluqdakı faylları göstər
            self.status_var.set("Cari qovluqdakı fayllar yoxlanılır...")
            
            # unzip.php faylını yarat və yüklə
            unzip_php = """<?php
header("Content-Type: text/plain");
set_time_limit(0);
error_reporting(E_ALL);
ini_set('display_errors', 1);

$zip = new ZipArchive();
$zip_file = '%s';

if (!file_exists($zip_file)) {
    $extracted_marker = "extracted_" . basename($zip_file) . ".txt";
    if (file_exists($extracted_marker)) {
        echo "SUCCESS: Fayllar artıq çıxarılıb";
        @unlink($extracted_marker);
        // PHP faylını da sil
        @unlink(__FILE__);
    } else {
        echo "SUCCESS: Fayllar çıxarılıb";
        @unlink(__FILE__);
    }
    exit;
}

$res = $zip->open($zip_file);
if ($res === TRUE) {
    if ($zip->extractTo('.')) {
        $zip->close();
        @unlink($zip_file);
        
        // Marker faylı yarat
        $marker = fopen("extracted_" . basename($zip_file) . ".txt", "w");
        fwrite($marker, date('Y-m-d H:i:s'));
        fclose($marker);
        
        echo "SUCCESS: Fayllar uğurla çıxarıldı";
        // PHP faylını sil
        @unlink(__FILE__);
    } else {
        echo "Fayllar çıxarıla bilmədi";
        // Xəta halında da PHP faylını sil
        @unlink(__FILE__);
    }
} else {
    echo "ZIP faylı açıla bilmədi: " . $res;
    // Xəta halında da PHP faylını sil
    @unlink(__FILE__);
}
?>""" % (os.path.basename(zip_path))
            
            # PHP faylını yüklə
            unzip_file = "unzip_" + datetime.now().strftime("%Y%m%d_%H%M%S") + ".php"
            
            try:
                self.status_var.set(f"PHP faylı yaradılır: {unzip_file}")
                self.root.update_idletasks()
                
                # PHP faylını yüklə
                self.ftp.storbinary(f'STOR {unzip_file}', io.BytesIO(unzip_php.encode('utf-8')))
                time.sleep(2)
                
                # URL-i hazırla
                server_host = self.ftp_host.get()
                if server_host.startswith('ftp.'):
                    server_host = server_host[4:]
                
                current_path = self.ftp.pwd().strip('/')
                for prefix in ['/public_html/', '/www/', '/httpdocs/']:
                    if prefix in current_path:
                        current_path = current_path[current_path.find(prefix) + len(prefix):]
                
                # URL-ləri hazırla
                urls = []
                if current_path:
                    urls.append(f"http://{server_host}/{current_path}/{unzip_file}")
                    urls.append(f"http://www.{server_host}/{current_path}/{unzip_file}")
                else:
                    urls.append(f"http://{server_host}/{unzip_file}")
                    urls.append(f"http://www.{server_host}/{unzip_file}")
                
                # Hər iki URL-i yoxla və ilk işləyəni aç
                success = False
                error_message = ""
                
                for url in urls:
                    try:
                        self.status_var.set(f"URL yoxlanılır: {url}")
                        self.root.update_idletasks()
                        
                        # URL-i yoxla
                        response = urllib.request.urlopen(url)
                        if response.getcode() == 200:
                            success = True
                            break
                    except Exception as e:
                        error_message = str(e)
                        continue
                
                if not success:
                    raise Exception(f"PHP faylına çatmaq mümkün olmadı. Son xəta: {error_message}")
                
                # İstifadəçidən təsdiq gözlə
                if messagebox.askyesno("Təsdiq", 
                    "Fayllar serverə yükləndi.\n\n" +
                    "1. 'SUCCESS' mesajını gördükdən sonra 'Yes' düyməsini basın\n" +
                    "2. Əgər xəta mesajı görsəniz, 'No' düyməsini basın"):
                    self.status_var.set("ZIP faylı uğurla açıldı")
                else:
                    raise Exception("ZIP faylı açıla bilmədi")
                
            except Exception as e:
                raise Exception(f"PHP faylı ilə bağlı xəta: {str(e)}")
            
            finally:
                # Təmizlik - bütün müvəqqəti faylları sil
                try:
                    # PHP faylını sil
                    self.ftp.delete(unzip_file)
                except:
                    pass
                    
                try:
                    # ZIP faylını sil
                    self.ftp.delete(os.path.basename(zip_path))
                except:
                    pass
                    
                try:
                    # Marker faylını sil
                    self.ftp.delete(f"extracted_{os.path.basename(zip_path)}.txt")
                except:
                    pass
                
        except Exception as e:
            raise Exception(f"ZIP faylı açıla bilmədi: {str(e)}")
            
    def _try_change_directory(self, path):
        """Təhlükəsiz şəkildə qovluğa keçməyə çalışır"""
        try:
            current = self.ftp.pwd()  # Cari qovluğu yadda saxla
            self.ftp.cwd(path)
            return True
        except:
            try:
                self.ftp.cwd(current)  # Xəta baş verərsə əvvəlki qovluğa qayıt
            except:
                pass
            return False
            
    def _upload_files(self, files_to_upload):
        try:
            if not files_to_upload:
                return
                
            self.status_var.set("Fayllar ZIP-lənir...")
            self.progress_var.set(0)
            self.root.update_idletasks()
            
            try:
                # Faylları ZIP-lə
                zip_path = self._create_zip_file(files_to_upload)
                
                # ZIP faylının ölçüsünü al
                file_size = os.path.getsize(zip_path)
                uploaded = 0
                
                def callback(data):
                    nonlocal uploaded
                    uploaded += len(data)
                    progress = (uploaded / file_size) * 100
                    self.progress_var.set(progress)
                    self.status_var.set(f"ZIP yüklənir: {uploaded}/{file_size} bayt")
                    self.root.update_idletasks()
                    
                # ZIP faylını yüklə
                zip_name = f"upload_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
                with open(zip_path, 'rb') as file:
                    self.ftp.storbinary(f'STOR {zip_name}', file, callback=callback)
                    
                self.status_var.set("ZIP faylı açılır...")
                self.root.update_idletasks()
                
                # ZIP faylını serverdə aç
                self._extract_zip_on_server(zip_name, ".")
                    
            finally:
                # Müvəqqəti ZIP faylını sil
                if 'zip_path' in locals() and os.path.exists(zip_path):
                    os.unlink(zip_path)
                    
            self.root.after(0, self.upload_complete)
            
        except Exception as e:
            self.root.after(0, messagebox.showerror, "Xəta", 
                f"Yükləmə xətası: {str(e)}\n\n" +
                "Tövsiyələr:\n" +
                "1. Düzgün qovluğu seçdiyinizdən əmin olun\n" +
                "2. Qovluq icazələrini yoxlayın\n" +
                "3. FTP məlumatlarının düzgünlüyünü yoxlayın"
            )
            
        finally:
            self.is_uploading = False
            
    def upload_complete(self):
        self.progress_var.set(100)
        self.status_var.set("Yükləmə tamamlandı və fayllar açıldı")
        messagebox.showinfo("Uğurlu", "Bütün fayllar uğurla yükləndi və ZIP-dən çıxarıldı!")
        self.refresh_server()

    def delete_selected(self):
        """Seçilmiş fayl və ya qovluğu sil"""
        if not self.ftp:
            messagebox.showwarning("Xəbərdarlıq", "Əvvəlcə FTP serverə bağlanın!")
            return
            
        selection = self.folder_listbox.curselection()
        if not selection:
            messagebox.showwarning("Xəbərdarlıq", "Silmək üçün fayl və ya qovluq seçin!")
            return
            
        item = self.folder_listbox.get(selection[0])
        if item == "../":
            messagebox.showwarning("Xəbərdarlıq", "Yuxarı qovluğu silmək olmaz!")
            return
            
        # Prefixi sil
        name = item[2:] if item.startswith(("📁 ", "📄 ")) else item
        
        if not messagebox.askyesno("Təsdiq", f"'{name}' silinsin?"):
            return
            
        try:
            # Əvvəlcə fayl kimi silməyə çalış
            try:
                self.ftp.delete(name)
                self.refresh_server()
                self.status_var.set(f"'{name}' silindi")
                return
            except:
                pass
                
            # Fayl kimi silinmədisə, qovluq kimi silməyə çalış
            try:
                self.delete_directory(name)
                self.refresh_server()
                self.status_var.set(f"'{name}' qovluğu silindi")
            except Exception as e:
                raise Exception(f"Qovluq silinə bilmədi: {str(e)}")
                
        except Exception as e:
            messagebox.showerror("Xəta", str(e))
            
    def delete_directory(self, dirname):
        """Rekursiv olaraq qovluğu və içindəkiləri sil"""
        try:
            # Cari mövqeyi yadda saxla
            original_dir = self.ftp.pwd()
            
            # Qovluğa daxil ol
            self.ftp.cwd(dirname)
            
            # Qovluqdakı bütün elementləri al
            items = []
            self.ftp.dir(items.append)
            
            # Hər bir elementi sil
            for item in items:
                # FTP LIST formatını parse et
                parts = item.split()
                name = " ".join(parts[8:])
                
                if name in [".", ".."]:
                    continue
                    
                # Qovluqdursa rekursiv sil, faildırsa birbaşa sil
                if item.startswith('d'):
                    self.delete_directory(name)
                else:
                    self.ftp.delete(name)
                    
            # Əvvəlki qovluğa qayıt
            self.ftp.cwd(original_dir)
            
            # Boş qovluğu sil
            self.ftp.rmd(dirname)
            
        except Exception as e:
            raise Exception(f"Qovluq silinərkən xəta: {str(e)}")
            
    def rename_selected(self):
        """Seçilmiş fayl və ya qovluğun adını dəyiş"""
        if not self.ftp:
            messagebox.showwarning("Xəbərdarlıq", "Əvvəlcə FTP serverə bağlanın!")
            return
            
        selection = self.folder_listbox.curselection()
        if not selection:
            messagebox.showwarning("Xəbərdarlıq", "Ad dəyişmək üçün fayl və ya qovluq seçin!")
            return
            
        item = self.folder_listbox.get(selection[0])
        if item == "../":
            messagebox.showwarning("Xəbərdarlıq", "Yuxarı qovluğun adını dəyişmək olmaz!")
            return
            
        # Prefixi sil
        old_name = item[2:] if item.startswith(("📁 ", "📄 ")) else item
        
        # Yeni adı soruş
        new_name = simpledialog.askstring(
            "Adı Dəyiş",
            "Yeni adı daxil edin:",
            initialvalue=old_name
        )
        
        if not new_name or new_name == old_name:
            return
            
        try:
            self.ftp.rename(old_name, new_name)
            self.refresh_server()
            self.status_var.set(f"'{old_name}' -> '{new_name}' adı dəyişdirildi")
        except Exception as e:
            messagebox.showerror("Xəta", f"Ad dəyişmə xətası: {str(e)}")

    def show_context_menu(self, event):
        """Kontekst menyusunu göstər"""
        if not self.ftp:
            messagebox.showwarning("Xəbərdarlıq", "Əvvəlcə FTP serverə bağlanın!")
            return
            
        # Klikləmə koordinatlarını al
        click_x = event.x
        click_y = event.y
        
        # Klikləmə nöqtəsindəki elementi tap
        clicked_index = self.folder_listbox.nearest(click_y)
        
        # Əgər boş yerə klik edilibsə
        if clicked_index >= self.folder_listbox.size() or click_x < 0 or click_y < 0:
            # Seçimi təmizlə
            self.folder_listbox.selection_clear(0, tk.END)
            
            # Yalnız "Yeni Qovluq" göstər
            self.context_menu.entryconfig("Sil", state="disabled")
            self.context_menu.entryconfig("Adını Dəyiş", state="disabled")
        else:
            # Elementi seç
            self.folder_listbox.selection_clear(0, tk.END)
            self.folder_listbox.selection_set(clicked_index)
            
            # Elementə görə menyu elementlərini aktivləşdir
            item = self.folder_listbox.get(clicked_index)
            if item == "../":
                self.context_menu.entryconfig("Sil", state="disabled")
                self.context_menu.entryconfig("Adını Dəyiş", state="disabled")
            else:
                self.context_menu.entryconfig("Sil", state="normal")
                self.context_menu.entryconfig("Adını Dəyiş", state="normal")
                
        # Menyunu göstər
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def toggle_checkbox(self, event):
        # Klikləmə nöqtəsindəki elementi tap
        index = self.file_listbox.nearest(event.y)
        if index < 0:
            return
            
        # Elementin mətni
        text = self.file_listbox.get(index)
        
        # Checkbox simvolunun mövqeyi
        if text.startswith("☑"):
            new_text = "☐" + text[1:]
            is_checked = False
        elif text.startswith("☐"):
            new_text = "☑" + text[1:]
            is_checked = True
        else:
            return
            
        # Fayl yolunu tap
        file_path = None
        for path in self.file_checkboxes:
            if os.path.basename(path) in text:
                file_path = path
                break
                
        if file_path:
            # Checkbox dəyişənini yenilə
            self.file_checkboxes[file_path].set(is_checked)
            # Siyahıda mətni yenilə
            self.file_listbox.delete(index)
            self.file_listbox.insert(index, new_text)
            
            # Seçilmiş faylların sayını yenilə
            checked_count = sum(1 for var in self.file_checkboxes.values() if var.get())
            self.status_var.set(f"{checked_count}/{len(self.selected_files)} fayl seçildi")

if __name__ == "__main__":
    root = tk.Tk()
    style = ttk.Style()
    style.theme_use("clam")
    app = FTPUploader(root)
    root.mainloop() 