import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import re

class AutoHotkeyEditor:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("AutoHotkey Kısayol Düzenleyici")
        self.window.geometry("800x600")
        
        # AHK dosya yolu
        self.ahk_path = r"C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\acilish.ahk"
        
        # Ana frame
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Sol panel - Yeni kısayol ekleme
        left_frame = ttk.LabelFrame(main_frame, text="Yeni Kısayol Ekle")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Kısayol seçimi
        ttk.Label(left_frame, text="Kısayol:").pack(pady=5)
        self.shortcut_frame = ttk.Frame(left_frame)
        self.shortcut_frame.pack(pady=5)
        
        # Modifier tuşları
        self.ctrl_var = tk.BooleanVar(value=True)
        self.alt_var = tk.BooleanVar(value=False)
        self.shift_var = tk.BooleanVar(value=False)
        
        ttk.Checkbutton(self.shortcut_frame, text="Ctrl", variable=self.ctrl_var).pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(self.shortcut_frame, text="Alt", variable=self.alt_var).pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(self.shortcut_frame, text="Shift", variable=self.shift_var).pack(side=tk.LEFT, padx=5)
        
        # Tuş kombinasyonu girişi
        self.key_entry = ttk.Entry(self.shortcut_frame, width=20)
        self.key_entry.pack(side=tk.LEFT, padx=5)
        self.key_entry.bind('<Key>', self.on_key_press)
        self.key_entry.insert(0, "Tuş kombinasyonu girmek için tıklayın...")
        self.key_entry.config(state='readonly')
        
        # Komut girişi
        ttk.Label(left_frame, text="Komut:").pack(pady=5)
        self.command_frame = ttk.Frame(left_frame)
        self.command_frame.pack(pady=5, fill=tk.BOTH, expand=True)
        
        # Text widget ile scrollbar
        self.command_text = tk.Text(self.command_frame, height=8, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(self.command_frame, orient=tk.VERTICAL, command=self.command_text.yview)
        self.command_text.configure(yscrollcommand=scrollbar.set)
        
        self.command_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Varsayılan metin
        self.command_text.insert("1.0", "")
        
        # Kaydet butonu
        self.save_button = ttk.Button(left_frame, text="Kısayolu Kaydet", command=self.save_shortcut)
        self.save_button.pack(pady=20)
        
        # Sağ panel - Mevcut kısayollar
        right_frame = ttk.LabelFrame(main_frame, text="Mevcut Kısayollar")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Kısayol listesi
        self.shortcut_list = ttk.Treeview(right_frame, columns=("Kısayol", "Komut"), show="headings")
        self.shortcut_list.heading("Kısayol", text="Kısayol")
        self.shortcut_list.heading("Komut", text="Komut/Metin")
        self.shortcut_list.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Düzenle/Sil butonları
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(button_frame, text="Düzenle", command=self.edit_shortcut).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Sil", command=self.delete_shortcut).pack(side=tk.LEFT, padx=5)
        
        # Mevcut kısayolları yükle
        self.load_shortcuts()
    
    def convert_ahk_to_readable(self, ahk_shortcut):
        """AHK kısayol formatını okunabilir formata çevirir"""
        # Önce yorum kısmını kaldır
        if ';' in ahk_shortcut:
            ahk_shortcut = ahk_shortcut.split(';')[0].strip()
        
        # :: kısmını kaldır
        if '::' in ahk_shortcut:
            ahk_shortcut = ahk_shortcut.split('::')[0].strip()
        
        # Modifierleri çevir
        readable = ""
        if '^' in ahk_shortcut:
            readable += "Ctrl+"
        if '!' in ahk_shortcut:
            readable += "Alt+"
        if '+' in ahk_shortcut:
            readable += "Shift+"
        
        # Son tuşu al (son karakter)
        key = ahk_shortcut[-1].upper()
        readable += key
        
        return readable
    
    def load_shortcuts(self):
        """Mevcut kısayolları yükle"""
        if not os.path.exists(self.ahk_path):
            return
            
        with open(self.ahk_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Listeyi temizle
        for item in self.shortcut_list.get_children():
            self.shortcut_list.delete(item)
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if '::' in line:
                shortcut = self.convert_ahk_to_readable(line)
                command_lines = []
                i += 1  # Kısayol satırından sonraki satıra geç
                
                # return görene kadar tüm komut satırlarını topla
                while i < len(lines) and 'return' not in lines[i].strip().lower():
                    cmd_line = lines[i].strip()
                    if cmd_line:  # Boş satırları alma
                        command_lines.append(cmd_line)
                    i += 1
                    
                # Tüm komut satırlarını birleştir
                command = '\n'.join(command_lines)
                if command:  # Boş komut ekleme
                    self.shortcut_list.insert("", tk.END, values=(shortcut, command))
            i += 1
    
    def browse_file(self):
        filename = filedialog.askopenfilename(
            title="Uygulama Seç",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if filename:
            self.command_entry.delete(0, tk.END)
            self.command_entry.insert(0, filename)
    
    def on_key_press(self, event):
        """Tuş kombinasyonunu yakala"""
        # Modifier tuşlarını yoksay
        if event.keysym in ['Control_L', 'Control_R', 'Alt_L', 'Alt_R', 'Shift_L', 'Shift_R']:
            return 'break'
            
        # Event state'den modifier durumlarını kontrol et
        ctrl_pressed = (event.state & 0x4) != 0  # Control
        alt_pressed = (event.state & 0x8) != 0   # Alt  
        shift_pressed = (event.state & 0x1) != 0 # Shift
        
        # Checkbox'ları güncelle
        self.ctrl_var.set(ctrl_pressed)
        self.alt_var.set(alt_pressed)
        self.shift_var.set(shift_pressed)
        
        # Tuş kombinasyonunu oluştur
        shortcut_text = []
        if ctrl_pressed:
            shortcut_text.append("Ctrl")
        if alt_pressed:
            shortcut_text.append("Alt")
        if shift_pressed:
            shortcut_text.append("Shift")
        
        # Ana tuşu ekle
        key = event.keysym.upper()
        if key == 'SPACE':
            key = 'Space'
        shortcut_text.append(key)
        
        # Entry'yi güncelle
        self.key_entry.config(state='normal')
        self.key_entry.delete(0, tk.END)
        self.key_entry.insert(0, "+".join(shortcut_text))
        self.key_entry.config(state='readonly')
        
        return 'break'
    
    def on_key_press_edit(self, event, entry, ctrl_var, alt_var, shift_var):
        """Düzenleme penceresinde tuş kombinasyonunu yakala"""
        # Modifier tuşlarını yoksay
        if event.keysym in ['Control_L', 'Control_R', 'Alt_L', 'Alt_R', 'Shift_L', 'Shift_R']:
            return 'break'
            
        # Event state'den modifier durumlarını kontrol et
        ctrl_pressed = (event.state & 0x4) != 0  # Control
        alt_pressed = (event.state & 0x8) != 0   # Alt  
        shift_pressed = (event.state & 0x1) != 0 # Shift
        
        # Checkbox'ları güncelle
        ctrl_var.set(ctrl_pressed)
        alt_var.set(alt_pressed)
        shift_var.set(shift_pressed)
        
        # Tuş kombinasyonunu oluştur
        shortcut_text = []
        if ctrl_pressed:
            shortcut_text.append("Ctrl")
        if alt_pressed:
            shortcut_text.append("Alt")
        if shift_pressed:
            shortcut_text.append("Shift")
        
        # Ana tuşu ekle
        key = event.keysym.upper()
        if key == 'SPACE':
            key = 'Space'
        shortcut_text.append(key)
        
        # Entry'yi güncelle
        entry.config(state='normal')
        entry.delete(0, tk.END)
        entry.insert(0, "+".join(shortcut_text))
        entry.config(state='readonly')
        
        return 'break'
    
    def save_shortcut(self):
        shortcut = self.key_entry.get().strip()
        command = self.command_text.get("1.0", tk.END).strip()
        
        if shortcut == "Tuş kombinasyonu girmek için tıklayın..." or not command:
            messagebox.showerror("Hata", "Lütfen tuş kombinasyonu ve komut girin!")
            return
            
        if not any([self.ctrl_var.get(), self.alt_var.get(), self.shift_var.get()]):
            messagebox.showerror("Hata", "En az bir modifier tuş seçmelisiniz!")
            return
        
        # AHK formatında modifierları hazırla
        modifiers = ""
        if self.ctrl_var.get(): 
            modifiers += "^"
        if self.alt_var.get(): 
            modifiers += "!"
        if self.shift_var.get(): 
            modifiers += "+"
            
        key = shortcut.split("+")[-1]
        
        # AHK formatında yeni kısayol
        new_shortcut = f"{modifiers}{key}:: ; {shortcut} için\n{command}\nreturn\n"
        
        # Dosyaya ekle
        with open(self.ahk_path, 'a', encoding='utf-8') as f:
            f.write(new_shortcut)
        
        # AutoHotkey'i yeniden başlat
        os.system(f'taskkill /f /im AutoHotkey.exe')
        os.startfile(self.ahk_path)
        
        messagebox.showinfo("Başarılı", f"{shortcut} kısayolu eklendi!")
        
        # Alanları temizle
        self.key_entry.config(state='normal')
        self.key_entry.delete(0, tk.END)
        self.key_entry.insert(0, "Tuş kombinasyonu girmek için tıklayın...")
        self.key_entry.config(state='readonly')
        
        self.command_text.delete("1.0", tk.END)
        self.command_text.insert("1.0", "Send Merhaba, nasılsın?\nRun notepad.exe")
        
        # Listeyi güncelle
        self.load_shortcuts()
    
    def edit_shortcut(self):
        selected = self.shortcut_list.selection()
        if not selected:
            messagebox.showwarning("Uyarı", "Düzenlemek için bir kısayol seçin!")
            return
            
        item = self.shortcut_list.item(selected[0])
        old_shortcut, old_command = item['values']
        
        # Düzenleme penceresini aç
        edit_window = tk.Toplevel(self.window)
        edit_window.title("Kısayolu Düzenle")
        edit_window.geometry("500x400")
        edit_window.grab_set()  # Modal pencere yap
        
        # Mevcut kısayol bilgisi
        ttk.Label(edit_window, text=f"Mevcut Kısayol: {old_shortcut}").pack(pady=5)
        
        # Yeni kısayol seçimi
        ttk.Label(edit_window, text="Yeni Kısayol:").pack(pady=5)
        shortcut_frame = ttk.Frame(edit_window)
        shortcut_frame.pack(pady=5)
        
        # Modifier tuşları
        ctrl_var = tk.BooleanVar(value="Ctrl" in old_shortcut)
        alt_var = tk.BooleanVar(value="Alt" in old_shortcut)
        shift_var = tk.BooleanVar(value="Shift" in old_shortcut)
        
        ttk.Checkbutton(shortcut_frame, text="Ctrl", variable=ctrl_var).pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(shortcut_frame, text="Alt", variable=alt_var).pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(shortcut_frame, text="Shift", variable=shift_var).pack(side=tk.LEFT, padx=5)
        
        # Tuş kombinasyonu girişi
        key_entry = ttk.Entry(shortcut_frame, width=20)
        key_entry.pack(side=tk.LEFT, padx=5)
        key_entry.bind('<Key>', lambda e: self.on_key_press_edit(e, key_entry, ctrl_var, alt_var, shift_var))
        key_entry.insert(0, old_shortcut)
        key_entry.config(state='readonly')
        
        # Komut girişi
        ttk.Label(edit_window, text="Komut:").pack(pady=(20,5))
        command_frame = ttk.Frame(edit_window)
        command_frame.pack(pady=5, fill=tk.X, padx=10)
        
        # Text widget ile scrollbar
        command_text = tk.Text(command_frame, height=6, wrap=tk.WORD)
        command_scrollbar = ttk.Scrollbar(command_frame, orient=tk.VERTICAL, command=command_text.yview)
        command_text.configure(yscrollcommand=command_scrollbar.set)
        
        command_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        command_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        command_text.insert("1.0", old_command)
        
        # Butonlar
        button_frame = ttk.Frame(edit_window)
        button_frame.pack(pady=20)
        
        def save():
            shortcut = key_entry.get().strip()
            new_command = command_text.get("1.0", tk.END).strip()
            
            if not shortcut or not new_command:
                messagebox.showerror("Hata", "Lütfen tüm alanları doldurun!")
                return
                
            if not any([ctrl_var.get(), alt_var.get(), shift_var.get()]):
                messagebox.showerror("Hata", "En az bir modifier tuş seçmelisiniz!")
                return
            
            # Yeni kısayol metnini oluştur
            new_modifiers = ""
            if ctrl_var.get(): 
                new_modifiers += "^"
            if alt_var.get(): 
                new_modifiers += "!"
            if shift_var.get(): 
                new_modifiers += "+"
                
            key = shortcut.split("+")[-1]
            
            # Dosyayı oku
            with open(self.ahk_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Eski kısayolu bul ve sil
            i = 0
            while i < len(lines):
                if '::' in lines[i]:
                    start_idx = i
                    i += 1
                    while i < len(lines) and 'return' not in lines[i].strip().lower():
                        i += 1
                    if i < len(lines):
                        # Kısayol bloğunun içeriğini kontrol et
                        block_content = ''.join(lines[start_idx:i+1])
                        if any(cmd in block_content for cmd in old_command.split('\n')):
                            del lines[start_idx:i+1]
                            break
                i += 1
            
            # Yeni kısayolu ekle
            new_shortcut = f"{new_modifiers}{key}:: ; {shortcut} için\n{new_command}\nreturn\n"
            lines.append(new_shortcut)
            
            # Dosyaya kaydet
            with open(self.ahk_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            # AutoHotkey'i yeniden başlat
            os.system(f'taskkill /f /im AutoHotkey.exe')
            os.startfile(self.ahk_path)
            
            messagebox.showinfo("Başarılı", f"Kısayol {shortcut} olarak güncellendi!")
            edit_window.destroy()
            self.load_shortcuts()
        
        def cancel():
            edit_window.destroy()
        
        ttk.Button(button_frame, text="Kaydet", command=save).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="İptal", command=cancel).pack(side=tk.LEFT, padx=5)
    
    def delete_shortcut(self):
        selected = self.shortcut_list.selection()
        if not selected:
            messagebox.showwarning("Uyarı", "Silmek için bir kısayol seçin!")
            return
            
        if not messagebox.askyesno("Onay", "Seçili kısayolu silmek istediğinizden emin misiniz?"):
            return
            
        item = self.shortcut_list.item(selected[0])
        shortcut, command = item['values']
        
        # Dosyayı oku
        with open(self.ahk_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Kısayolu bul ve sil
        i = 0
        while i < len(lines):
            if '::' in lines[i]:
                start_idx = i
                i += 1
                while i < len(lines) and 'return' not in lines[i].strip().lower():
                    i += 1
                if i < len(lines):
                    # Kısayol bloğunun içeriğini kontrol et
                    block_content = ''.join(lines[start_idx:i+1])
                    if any(cmd in block_content for cmd in command.split('\n')):
                        del lines[start_idx:i+1]
                        break
            i += 1
        
        # Dosyaya kaydet
        with open(self.ahk_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        # AutoHotkey'i yeniden başlat
        os.system(f'taskkill /f /im AutoHotkey.exe')
        os.startfile(self.ahk_path)
        
        messagebox.showinfo("Başarılı", "Kısayol silindi!")
        self.load_shortcuts()

if __name__ == "__main__":
    app = AutoHotkeyEditor()
    app.window.mainloop()
