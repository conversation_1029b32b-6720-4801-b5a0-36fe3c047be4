import tkinter as tk
from tkinter import messagebox

def hesabla():
    try:
        alis = float(entry_alis.get())
        if alis <= 0:
            raise ValueError("<PERSON><PERSON><PERSON> qiyməti müsbət olmalıdır.")
    except ValueError as e:
        messagebox.showerror("Xəta", f"Alış qiymətində səhv: {e}")
        return

    try:
        xalis_qazanc_faizi = float(entry_qazanc.get())
        if not (0 <= xalis_qazanc_faizi <= 100):
            raise ValueError("Faiz 0 ilə 100 arasında olmalıdır.")
        xalis_qazanc_faizi /= 100
    except ValueError as e:
        messagebox.showerror("Xəta", f"Xalis qazanc faizində səhv: {e}")
        return

    menfeet_vergisi_faizi = 0.20
    edv_faizi = 0.18

    satis_edv_daxil = (alis + alis * xalis_qazanc_faizi / (1 - menfeet_vergisi_faizi)) * (1 + edv_faizi)
    edv_meblegi = satis_edv_daxil * (edv_faizi / (1 + edv_faizi))
    satis_edv_siz = satis_edv_daxil - edv_meblegi
    menfeet = satis_edv_siz - alis
    menfeet_vergisi = menfeet * menfeet_vergisi_faizi
    xalis_qazanc = menfeet - menfeet_vergisi

    result_text = (
        f"\n=== YEKUN SATIŞ QİYMƏTİ ===\n"
        f"{satis_edv_daxil:.2f} MANAT (ƏDV daxil)\n"
        f"========================\n\n"
        f"ƏDV məbləği: {edv_meblegi:.2f} manat\n"
        f"Satış qiyməti (ƏDV-siz): {satis_edv_siz:.2f} manat\n"
        f"Mənfəət (ƏDV-siz): {menfeet:.2f} manat\n"
        f"Mənfəət vergisi (20%): {menfeet_vergisi:.2f} manat\n"
        f"Xalis qazanc: {xalis_qazanc:.2f} manat\n"
        f"İstədiyiniz xalis qazanc faizi: {xalis_qazanc_faizi*100:.2f}%"
    )

    lbl_netice.config(text=result_text, font=("Consolas", 11))

root = tk.Tk()
root.title("Satış Qiyməti və Vergi Hesablama")
root.geometry("600x550")
root.resizable(False, False)

font_label = ("Segoe UI", 11)
font_entry = ("Segoe UI", 11)
font_button = ("Segoe UI", 11, "bold")
font_result = ("Consolas", 11)

# Main container frame
main_frame = tk.Frame(root, padx=30)
main_frame.pack(fill="both", expand=True)

tk.Label(main_frame, text="Məhsulun alış qiyməti (manat):", font=font_label).pack(pady=(25,8))
entry_alis = tk.Entry(main_frame, font=font_entry, width=15, justify='center')
entry_alis.pack(pady=5, ipady=3)

tk.Label(main_frame, text="İstədiyiniz xalis qazanc faizi (%):", font=font_label).pack(pady=(20,8))
entry_qazanc = tk.Entry(main_frame, font=font_entry, width=15, justify='center')
entry_qazanc.insert(0, "20")
entry_qazanc.pack(pady=5, ipady=3)

btn_hesabla = tk.Button(main_frame, text="HESABLA", font=font_button, 
                        command=hesabla, width=12, height=1)
btn_hesabla.pack(pady=25)

lbl_netice = tk.Label(main_frame, text="", font=font_result, justify="left", 
                      fg="black", anchor="nw", wraplength=400)
lbl_netice.pack(fill="both", expand=True)

root.mainloop()
