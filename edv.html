<!DOCTYPE html>
<html lang="az">
<head>
<meta charset="UTF-8" />
<title>ƏDV, Mənfəət Vergisi və Satış Qiyməti Hesablama</title>
<style>
  body {
    font-family: Arial, sans-serif;
    max-width: 450px;
    margin: 50px auto;
    padding: 20px;
    border: 2px solid #b22222;
    border-radius: 10px;
    background-color: #fff8f8;
    color: #7b0000;
  }
  label {
    font-weight: bold;
    display: block;
    margin-top: 15px;
  }
  input[type=number] {
    width: 100%;
    font-size: 16px;
    padding: 6px;
    margin-top: 5px;
    border-radius: 5px;
    border: 1.5px solid #b22222;
  }
  .result {
    margin-top: 25px;
    background: #f9d6d6;
    padding: 15px;
    border-radius: 8px;
    font-size: 18px;
    line-height: 1.5;
  }
</style>
</head>
<body>

<h2>ƏDV, <PERSON>ənfəət Vergisi və Satış Qiyməti Hesablama</h2>

<label for="alisQiymeti">Mə<PERSON><PERSON>n alış qiyməti (manat):</label>
<input type="number" id="alisQiymeti" min="0" step="0.01" placeholder="Məsələn, 100" />

<label for="xalisQazancFaizi">İstədiyiniz xalis qazanc faizi (%):</label>
<input type="number" id="xalisQazancFaizi" min="0" max="100" step="0.1" value="20" />

<div class="result" id="netice">
  Satış qiyməti, ƏDV, mənfəət vergisi və xalis qazanc burada göstəriləcək.
</div>

<script>
  const alisInput = document.getElementById('alisQiymeti');
  const qazancInput = document.getElementById('xalisQazancFaizi');
  const neticeEl = document.getElementById('netice');

  function hesabla() {
    const alis = parseFloat(alisInput.value);
    const xalisQazancFaizi = parseFloat(qazancInput.value) / 100;

    if (isNaN(alis) || alis <= 0) {
      neticeEl.innerHTML = "Zəhmət olmasa, müsbət alış qiyməti daxil edin.";
      return;
    }
    if (isNaN(xalisQazancFaizi) || xalisQazancFaizi < 0 || xalisQazancFaizi > 1) {
      neticeEl.innerHTML = "Zəhmət olmasa, xalis qazanc faizini 0-100 arasında daxil edin.";
      return;
    }

    const menfeetVergisiFaizi = 0.20;  // 20%
    const edvFaizi = 0.18;  // 18%

    // Satış qiymətini tapmaq üçün tənlik:
    // (X * 100/118 - alis) * (1 - menfeetVergisiFaizi) = alis * xalisQazancFaizi
    // X * 100/118 = alis + alis * xalisQazancFaizi / (1 - menfeetVergisiFaizi)
    // X = (alis + alis * xalisQazancFaizi / (1 - menfeetVergisiFaizi)) * 118/100

    const satisEdvDaxil = (alis + alis * xalisQazancFaizi / (1 - menfeetVergisiFaizi)) * (118 / 100);

    // ƏDV məbləği:
    const edvMeblegi = satisEdvDaxil * (edvFaizi / (1 + edvFaizi));

    // ƏDV-siz satış qiyməti:
    const satisEdvSiz = satisEdvDaxil - edvMeblegi;

    // Mənfəət (ƏDV-siz satış - alış)
    const menfeet = satisEdvSiz - alis;

    // Mənfəət vergisi
    const menfeetVergisi = menfeet * menfeetVergisiFaizi;

    // Xalis qazanc (mənfəətdən vergi çıxdıqdan sonra)
    const xalisQazanc = menfeet - menfeetVergisi;

    neticeEl.innerHTML = `
      <strong>Hesablama nəticələri:</strong><br><br>
      <b>Satış qiyməti (ƏDV daxil):</b> ${satisEdvDaxil.toFixed(2)} manat<br>
      <b>ƏDV məbləği:</b> ${edvMeblegi.toFixed(2)} manat<br>
      <b>Satış qiyməti (ƏDV-siz):</b> ${satisEdvSiz.toFixed(2)} manat<br>
      <b>Mənfəət (ƏDV-siz satış - alış):</b> ${menfeet.toFixed(2)} manat<br>
      <b>Mənfəət vergisi (20%):</b> ${menfeetVergisi.toFixed(2)} manat<br>
      <b>Xalis qazanc (vergilər çıxdıqdan sonra):</b> ${xalisQazanc.toFixed(2)} manat<br><br>
      İstədiyiniz xalis qazanc faizi: ${(xalisQazancFaizi * 100).toFixed(2)}%
    `;
  }

  alisInput.addEventListener('input', hesabla);
  qazancInput.addEventListener('input', hesabla);

  // İlkin hesablama üçün çağırırıq
  hesabla();
</script>

</body>
</html>
