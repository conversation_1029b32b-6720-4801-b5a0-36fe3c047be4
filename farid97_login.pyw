import webbrowser
import pyautogui
import time
import sys

def login_with_autogui():
    """Bu funksiya brauzeri açır və klaviatura ilə daxil etməni təqlid edir"""
    try:
        # URL-ni açın
        url = "https://ha003.just.hosting:2222"
        webbrowser.open(url)
        
        # Səhifənin yüklənməsinə çox az vaxt verin - səhifə açılır açılmaz işləsin
        print("Giriş prosesi başlayır...")
        time.sleep(3)  # Daha az gözləmə
        
        # İstifadəçi təlimatları
        print("Avtomatik giriş edilir...")
        
        # İstifadəçi adını daxil edin
        pyautogui.write('s965345')
        time.sleep(0.001)
        
        # Tab düyməsini sıxaraq şifrə sahəsinə keçin
        pyautogui.press('tab')
        time.sleep(0.001)
        
        # Şifrəni daxil edin
        pyautogui.write('cTnIZDqg')
        time.sleep(0.001)
        
        # Enter düyməsini sıxaraq giriş edin
        pyautogui.press('enter')
        
        print("Giriş edildi!")
        
    except Exception as e:
        print(f"Xəta baş verdi: {e}")
        # Xəta halında istifadəçiyə giriş məlumatlarını göstərin
        print("URL: https://ha003.just.hosting:2222")
        print("İstifadəçi adı: s965345")
        print("Şifrə: cTnIZDqg")
    
    input("Çıxmaq üçün Enter düyməsini basın...")

if __name__ == "__main__":
    # Selenium-dən istifadə etməyək - sadə yanaşma
    try:
        # PyAutoGUI-nin quraşdırılıb-quraşdırılmadığını yoxlayın
        import pyautogui
        login_with_autogui()
    except ImportError:
        # PyAutoGUI quraşdırılmayıbsa, sadəcə brauzeri açın və məlumatları göstərin
        print("PyAutoGUI kitabxanası quraşdırılmayıb. Sadəcə brauzeri açıram...")
        webbrowser.open("https://ha003.just.hosting:2222")
        print("İstifadəçi adı: s965345")
        print("Şifrə: cTnIZDqg")
        input("Çıxmaq üçün Enter düyməsini basın...")
